<?php
/**
 * Test script to verify the email campaign template fix
 */

// Include necessary files
require_once 'system/startup_sequence_minimal.php';

use system\email_campaign;

try {
    echo "Testing email campaign template fix...\n\n";

    // Test campaign ID 2 (the one from your database entry)
    $campaign_id = 2;

    $campaign_manager = new email_campaign();
    
    // Get campaign details
    $campaign = $campaign_manager->get_campaign($campaign_id);
    if (!$campaign) {
        echo "❌ Campaign not found with ID: $campaign_id\n";
        exit(1);
    }
    
    echo "✅ Campaign found: {$campaign['name']}\n";
    echo "   Template file: {$campaign['template_file']}\n";
    echo "   Subject template: {$campaign['subject_template']}\n\n";
    
    // Test the get_template method
    $template = $campaign_manager->get_template($campaign_id);
    
    if (!$template) {
        echo "❌ No template found - the fix didn't work\n";
        exit(1);
    }
    
    echo "✅ Template found!\n";
    echo "   Template name: {$template['name']}\n";
    echo "   Template type: {$template['template_type']}\n";
    echo "   Subject template: " . ($template['subject_template'] ?: 'None') . "\n";
    echo "   Body template length: " . strlen($template['body_template']) . " characters\n";
    echo "   Placeholders found: " . count($template['placeholders']) . "\n";
    
    if (!empty($template['placeholders'])) {
        echo "   Placeholder list:\n";
        foreach ($template['placeholders'] as $key => $description) {
            echo "     - $key: $description\n";
        }
    }
    
    echo "\n✅ Template fix is working correctly!\n";
    echo "The 'Send Test Email' form should now work for this campaign.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
