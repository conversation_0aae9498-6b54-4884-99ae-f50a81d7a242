<?php

namespace api\email_campaigns;

use system\email_campaign;
use system\users;
use edge\Edge;
use autodesk_api\autodesk_api;
use Exception;

/**
 * Email Campaign Management API
 * 
 * Handles CRUD operations and campaign execution for the mass email system
 */

/**
 * Get campaigns list with data table format
 */
function get_campaigns($p = []) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    $campaign_manager = new email_campaign();
    
    $criteria = [];
    if (!empty($p['search'])) {
        $criteria['search'] = $p['search'];
    }
    if (!empty($p['status'])) {
        $criteria['status'] = $p['status'];
    }
    if (!empty($p['type'])) {
        $criteria['type'] = $p['type'];
    }
    
    $campaigns = $campaign_manager->get_campaigns($criteria);
    
    // Format for data table display
    $table_data = [];
    foreach ($campaigns as $campaign) {
        $table_data[] = [
            'id' => $campaign['id'],
            'name' => $campaign['name'],
            'type' => ucfirst(str_replace('_', ' ', $campaign['type'])),
            'status' => ucfirst($campaign['status']),
            'total_sent' => $campaign['total_sent'],
            'created_by_name' => $campaign['created_by_name'] ?? 'System',
            'created_at' => date('M j, Y', strtotime($campaign['created_at'])),
            'last_run_at' => $campaign['last_run_at'] ? date('M j, Y H:i', strtotime($campaign['last_run_at'])) : 'Never',
            'actions' => generate_campaign_actions($campaign)
        ];
    }
   // print_rr($table_data, 'table_data');
    return Edge::render('data-table', [
        'columns' => [
            ['label' => 'Name', 'field' => 'name'],
            ['label' => 'Type', 'field' => 'type'],
            ['label' => 'Status', 'field' => 'status'],
            ['label' => 'Total Sent', 'field' => 'total_sent'],
            ['label' => 'Created By', 'field' => 'created_by_name'],
            ['label' => 'Created', 'field' => 'created_at'],
            ['label' => 'Last Run', 'field' => 'last_run_at'],
            ['label' => 'Actions', 'field' => 'actions', 'sortable' => false]
        ],
        'items' => $table_data,
        'table_name' => 'campaigns_table',
        'show_column_manager' => false,
        'available_fields' => []
    ]);
}

/**
 * Generate action buttons for campaign row using Edge components
 */
function generate_campaign_actions($campaign) {
    $actions = [];

    // Edit button
    $actions[] = Edge::render('forms-button', [
        'type' => 'button',
        'label' => 'Edit',
        'icon' => 'pencil',
        'variant' => 'secondary',
        'size' => 'sm',
        'hx-get' => APP_ROOT . '/api/email_campaigns/edit_modal',
        'hx-target' => '#modal_body',
        'hx-vals' => '{"id":' . $campaign['id'] . '}',
        '@click' => 'showModal = true'
    ]);

    // View History button
    $actions[] = Edge::render('forms-button', [
        'type' => 'button',
        'label' => 'History',
        'icon' => 'clock',
        'variant' => 'secondary',
        'size' => 'sm',
        'hx-get' => APP_ROOT . '/api/email_campaigns/history_modal',
        'hx-target' => '#modal_body',
        'hx-vals' => '{"id":' . $campaign['id'] . '}',
        '@click' => 'showModal = true'
    ]);

    // Send Test button (if active)
    if ($campaign['status'] === 'active') {
        $actions[] = Edge::render('forms-button', [
            'type' => 'button',
            'label' => 'Test',
            'icon' => 'envelope',
            'variant' => 'primary',
            'size' => 'sm',
            'hx-get' => APP_ROOT . '/api/email_campaigns/test_modal',
            'hx-target' => '#modal_body',
            'hx-vals' => '{"id":' . $campaign['id'] . '}',
            '@click' => 'showModal = true'
        ]);
    }

    // Delete button (if not system campaign)
    if (!$campaign['is_system']) {
        $actions[] = Edge::render('forms-button', [
            'type' => 'button',
            'label' => 'Delete',
            'icon' => 'trash',
            'variant' => 'danger',
            'size' => 'sm',
            'hx-delete' => APP_ROOT . '/api/email_campaigns/delete',
            'hx-vals' => '{"id":' . $campaign['id'] . '}',
            'hx-confirm' => 'Are you sure you want to delete this campaign?',
            'hx-target' => '#campaigns_table',
            'hx-swap' => 'outerHTML'
        ]);
    }

    return '<div class="flex space-x-2">' . implode('', $actions) . '</div>';
}

/**
 * Create new campaign modal
 */
function create_modal($p = []) {
    users::requireRole(['admin', 'dev', 'manager']);

    return Edge::render('email-campaign-form', [
        'title' => 'Create New Email Campaign',
        'campaign' => [
            'name' => '',
            'description' => '',
            'type' => 'general',
            'status' => 'draft',
            'from_email' => '<EMAIL>',
            'from_name' => 'TCS CAD & BIM Solutions Limited',
            'subject_template' => ''
        ],
        'is_edit' => false,
        'campaign_types' => [
            'general' => 'General Campaign',
            'promotional' => 'Promotional',
            'notification' => 'Notification',
            'subscription_renewal' => 'Subscription Renewal'
        ]
    ]);
}

/**
 * Edit campaign modal
 */
function edit_modal($p) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    if (empty($p['id'])) {
        throw new Exception('Campaign ID is required');
    }
    
    $campaign_manager = new email_campaign();
    $campaign = $campaign_manager->get_campaign($p['id']);
    
    if (!$campaign) {
        throw new Exception('Campaign not found');
    }
    
    return Edge::render('email-campaign-form', [
        'title' => 'Edit Campaign: ' . $campaign['name'],
        'campaign' => $campaign,
        'is_edit' => true,
        'campaign_types' => [
            'general' => 'General Campaign',
            'promotional' => 'Promotional',
            'notification' => 'Notification',
            'subscription_renewal' => 'Subscription Renewal'
        ]
    ]);
}

/**
 * Save campaign (create or update)
 */
function save_campaign($p) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    $campaign_manager = new email_campaign();
    
    try {
        $data = [
            'name' => $p['name'] ?? '',
            'description' => $p['description'] ?? '',
            'type' => $p['type'] ?? 'general',
            'status' => $p['status'] ?? 'draft',
            'from_email' => $p['from_email'] ?? '',
            'from_name' => $p['from_name'] ?? '',
            'subject_template' => $p['subject_template'] ?? '',
            'email_template' => $p['email_template'] ?? '',
            'data_source_id' => !empty($p['data_source_id']) ? (int)$p['data_source_id'] : null
        ];

        // Handle subscription renewal settings
        if (($p['type'] ?? '') === 'subscription_renewal') {
            $send_rules = [];
            $send_schedule = [];

            // Process send rules (days before expiry)
            if (!empty($p['send_rules']) && is_array($p['send_rules'])) {
                $send_rules['days_before_expiry'] = array_filter($p['send_rules']);
            }

            // Process send days (days of week)
            if (!empty($p['send_days']) && is_array($p['send_days'])) {
                // Convert to boolean array for each day of week (0=Sunday, 6=Saturday)
                $days_array = [0, 0, 0, 0, 0, 0, 0]; // Default all false
                foreach ($p['send_days'] as $day) {
                    if (is_numeric($day) && $day >= 0 && $day <= 6) {
                        $days_array[(int)$day] = 1;
                    }
                }
                $send_schedule['send_days'] = $days_array;
            }

            // Process send time
            if (isset($p['send_time']) && is_numeric($p['send_time'])) {
                $send_schedule['send_time'] = (int)$p['send_time'];
            }

            // Store as JSON in the database
            if (!empty($send_rules)) {
                $data['send_rules'] = json_encode($send_rules);
            }
            if (!empty($send_schedule)) {
                $data['send_schedule'] = json_encode($send_schedule);
            }
        }
        
        if (!empty($p['id'])) {
            // Update existing campaign
            $success = $campaign_manager->update_campaign($p['id'], $data);
            $message = $success ? 'Campaign updated successfully' : 'Failed to update campaign';
        } else {
            // Create new campaign
            $campaign_id = $campaign_manager->create_campaign($data);
            $message = $campaign_id ? 'Campaign created successfully' : 'Failed to create campaign';
        }
        
        // Return success response with table refresh and modal close
        header('HX-Trigger: {"showNotification": {"type": "success", "message": "' . $message . '"}, "refreshCampaigns": {}}');
        return '<div class="text-center p-4">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Success!</h3>
                    <p class="text-sm text-gray-600 mb-4">' . $message . '</p>
                    <button type="button"
                            onclick="document.querySelector(\'[x-data]\').\_\_x.\$data.showModal = false"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>';
        
    } catch (Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . $e->getMessage() . '"}}');
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Delete campaign
 */
function delete($p) {
    users::requireRole(['admin', 'dev']);
    
    if (empty($p['id'])) {
        throw new Exception('Campaign ID is required');
    }
    
    $campaign_manager = new email_campaign();
    
    try {
        $success = $campaign_manager->delete_campaign($p['id']);
        
        if ($success) {
            header('HX-Trigger: {"showNotification": {"type": "success", "message": "Campaign deleted successfully"}}');
            return get_campaigns(); // Return refreshed table
        } else {
            throw new Exception('Failed to delete campaign');
        }
        
    } catch (Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . $e->getMessage() . '"}}');
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Campaign history modal
 */
function history_modal($p) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    if (empty($p['id'])) {
        throw new Exception('Campaign ID is required');
    }
    
    $campaign_manager = new email_campaign();
    $campaign = $campaign_manager->get_campaign($p['id']);
    $history = $campaign_manager->get_send_history($p['id'], ['limit' => 100]);
    
    return Edge::render('email-campaign-history', [
        'campaign' => $campaign,
        'history' => $history
    ]);
}

/**
 * Test email modal
 */
function test_modal($p) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    if (empty($p['id'])) {
        throw new Exception('Campaign ID is required');
    }
    
    $campaign_manager = new email_campaign();
    $campaign = $campaign_manager->get_campaign($p['id']);
    $template = $campaign_manager->get_template($p['id']);
    
    return Edge::render('email-campaign-test', [
        'campaign' => $campaign,
        'template' => $template
    ]);
}

/**
 * Send test email
 */
function send_test($p) {
    users::requireRole(['admin', 'dev', 'manager']);
    
    if (empty($p['campaign_id']) || empty($p['test_email'])) {
        throw new Exception('Campaign ID and test email are required');
    }
    
    $campaign_manager = new email_campaign();
    $campaign = $campaign_manager->get_campaign($p['campaign_id']);
    $template = $campaign_manager->get_template($p['campaign_id']);
    
    if (!$campaign || !$template) {
        throw new Exception('Campaign or template not found');
    }
    
    try {
        // Use existing email sending infrastructure
        $autodesk = new autodesk_api();
        
        // Create test data for placeholder replacement
        $test_data = [
            'endcust_primary_admin_email' => $p['test_email'],
            'endcust_primary_admin_first_name' => 'Test',
            'endcust_primary_admin_last_name' => 'User',
            'subs_product_name' => 'Test Product',
            'subs_endDate' => date('Y-m-d', strtotime('+30 days')),
            'subs_subscriptionReferenceNumber' => 'TEST-123456'
        ];
        
        // Replace placeholders in template
        $email_body = $autodesk->subscriptions->replace_placeholders($template['body_template'], $test_data);
        $subject = $autodesk->subscriptions->replace_placeholders($campaign['subject_template'], $test_data);
        
        // Send test email
        $result = $autodesk->subscriptions->send_email(
            $p['test_email'],
            $campaign['from_email'],
            '[TEST] ' . $subject,
            $email_body
        );
        
        // Record in history
        $campaign_manager->record_send_history([
            'campaign_id' => $p['campaign_id'],
            'recipient_email' => $p['test_email'],
            'recipient_name' => 'Test User',
            'subject' => '[TEST] ' . $subject,
            'send_status' => 'sent',
            'triggered_by_rule' => 'manual_test',
            'sent_at' => date('Y-m-d H:i:s')
        ]);
        
        header('HX-Trigger: {"showNotification": {"type": "success", "message": "Test email sent successfully"}}');
        return 'Test email sent to ' . $p['test_email'];
        
    } catch (Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to send test email: ' . $e->getMessage() . '"}}');
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Search campaigns (for HTMX search)
 */
function search($p) {
    return get_campaigns($p);
}

/**
 * Check template selection and redirect to editor
 */
function edit_template_check($p) {
    users::requireRole(['admin', 'dev', 'manager']);

    // Try different possible parameter names for the template
    $template = $p['template'] ?? $p['main_template_selector'] ?? $p['email_template'] ?? '';
    $campaign_id = $p['campaign_id'] ?? null;

    // Debug: Log the received parameters
    tcs_log("edit_template_check received parameters: " . json_encode($p), "email_campaigns_debug");

       if (empty($template)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Please select a template to edit"}}');
        return '<div class="text-center py-8">
                    <div class="text-red-600 font-medium">Please select a template to edit</div>
                    <div class="text-sm text-gray-500 mt-2">Debug: Received parameters - ' . htmlspecialchars(json_encode($p)) . '</div>
                    <button type="button"
                            onclick="document.querySelector(\'[x-data]\\').__x.$data.showModal = false"
                            class="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>';
    }

    return template_editor([
        'mode' => 'edit',
        'template' => $template,
        'campaign_id' => $campaign_id
    ]);
}

/**
 * Template editor modal
 */
function template_editor($p) {
    users::requireRole(['admin', 'dev', 'manager']);

    $mode = $p['mode'] ?? 'new';
    $template = $p['template'] ?? '';
    $campaign_id = $p['campaign_id'] ?? null;

    return Edge::render('email-template-editor', [
        'mode' => $mode,
        'template' => $template,
        'campaign_id' => $campaign_id
    ]);
}

/**
 * Save template
 */
function save_template($p) {
    users::requireRole(['admin', 'dev', 'manager']);

    try {
        $template_name = $p['template_name'] ?? '';
        $template_content = $p['template_content'] ?? '';
        $mode = $p['mode'] ?? 'new';
        $original_template = $p['original_template'] ?? '';

        if (empty($template_name) || empty($template_content)) {
            throw new Exception('Template name and content are required');
        }

        // Sanitize template name
        $template_name = preg_replace('/[^a-z0-9_-]/', '', strtolower($template_name));
        $filename = $template_name . '.emlt.php';
        $template_path = FS_APP_ROOT . '/resources/email_templates/' . $filename;

        // Create directory if it doesn't exist
        $template_dir = dirname($template_path);
        if (!is_dir($template_dir)) {
            mkdir($template_dir, 0755, true);
        }

        // If editing and filename changed, remove old file
        if ($mode === 'edit' && $original_template && $original_template !== $filename) {
            $old_path = FS_APP_ROOT . '/resources/email_templates/' . $original_template;
            if (file_exists($old_path)) {
                unlink($old_path);
            }
        }

        // Save template
        if (file_put_contents($template_path, $template_content) === false) {
            throw new Exception('Failed to save template file');
        }

        $message = $mode === 'edit' ? 'Template updated successfully' : 'Template created successfully';

        header('HX-Trigger: {"showNotification": {"type": "success", "message": "' . $message . '"}}');
        return '<div class="text-center py-8">
                    <div class="text-green-600 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="text-green-600 font-medium mb-4">' . $message . '</div>
                    <button type="button"
                            onclick="document.querySelector(\'[x-data]\\').__x.$data.showModal = false"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Close
                    </button>
                </div>';

    } catch (Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . $e->getMessage() . '"}}');
        return '<div class="text-center py-8 text-red-600">Error: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Template preview
 */
function template_preview($p) {
    // Try different possible parameter names for the template
    $template = $p['template'];

    if (empty($template)) {
        // Return hidden preview when no template selected
        return '<div id="template_preview" class="hidden mt-2 p-3 bg-gray-50 border rounded-md">
                    <div class="text-sm text-gray-600">
                        <strong>Preview:</strong>
                        <div id="template_preview_content" class="mt-1 max-h-32 overflow-y-auto text-xs"></div>
                    </div>
                </div>';
    }

    // Handle both direct files and subdirectory files
    $template_path = FS_APP_ROOT . '/resources/email_templates/' . $template;

    if (!file_exists($template_path)) {
        return '<div id="template_preview" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                    <div class="text-sm text-red-600">
                        <strong>Error:</strong> Template not found
                    </div>
                </div>';
    }

    $preview = file_get_contents($template_path);


    return '<div id="template_preview" class="mt-2 p-3 bg-gray-50 border rounded-md">
                <div class="text-sm text-gray-600">
                    <strong>Preview:</strong>
                    <div id="template_preview_content" class="mt-1 overflow-y-auto text-xs">' . $preview . '</div>
                </div>
            </div>';
}

/**
 * Close modal - returns empty content to close modal
 */
function close_modal($p = []) {
    users::requireRole(['admin', 'dev', 'manager']);

    // Return empty content with HX-Trigger to close modal
    header('HX-Trigger: {"closeModal": {}}');
    return '';
}

/**
 * Template preview in new window - server-side validation and window opening
 */
function template_preview_window($p) {
    users::requireRole(['admin', 'dev', 'manager']);

    // Try different possible parameter names for the template
    $template = $p['email_template'] ?? $p['template'] ?? $p['main_template_selector'] ?? '';

    // If no template found in expected parameters, look for any parameter that looks like a template file
    if (empty($template)) {
        foreach ($p as $key => $value) {
            if ($key === 'campaign_id' || empty($value)) {
                continue;
            }
            if (preg_match('/\.(emlt\.php|html?|php)$/i', $value)) {
                $template = $value;
                break;
            }
        }
    }

    if (empty($template)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Please select a template first"}}');
        return '';
    }

    // Return JavaScript to open new window
    $url = APP_ROOT . '/api/email_campaigns/template_preview?template=' . urlencode($template);
    header('HX-Trigger: {"openWindow": {"url": "' . $url . '"}}');
    return '';
}

/**
 * Template test with data source in new window
 */
function template_test_window($p) {
    users::requireRole(['admin', 'dev', 'manager']);

    // Try different possible parameter names for the template
    $template = $p['email_template'] ?? $p['template'] ?? $p['main_template_selector'] ?? '';
    $data_source = $p['data_source_id'] ?? '';

    // If no template found in expected parameters, look for any parameter that looks like a template file
    if (empty($template)) {
        foreach ($p as $key => $value) {
            if ($key === 'campaign_id' || $key === 'data_source_id' || empty($value)) {
                continue;
            }
            if (preg_match('/\.(emlt\.php|html?|php)$/i', $value)) {
                $template = $value;
                break;
            }
        }
    }

    if (empty($template)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Please select a template first"}}');
        return '';
    }

    if (empty($data_source)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Please select a data source to test with"}}');
        return '';
    }

    // Return JavaScript to open new window
    $url = APP_ROOT . '/email_campaigns/test?template=' . urlencode($template) . '&data_source=' . urlencode($data_source);
    header('HX-Trigger: {"openWindow": {"url": "' . $url . '"}}');
    return '';
}
